import { useState } from 'react'
import { useRouter } from 'next/router'

export function SearchIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="15399"
      width="16"
      height="16"
      {...props}
    >
      <path
        d="M848.08704 773.0432l75.02336 75.04896a53.0432 53.0432 0 0 1 3.8656 70.74816l-3.8656 4.3008c-20.69504 20.70528-54.28736 20.70528-75.02336 0l-75.02848-75.04896a53.05344 53.05344 0 0 1-15.48288-34.62656 53.05344 53.05344 0 0 1 11.58144-36.1216l3.90144-4.3008c20.69504-20.74112 54.29248-20.74112 75.02848 0zM456.77568 85.33504a371.4048 371.4048 0 0 1 262.656 108.82048 371.60448 371.60448 0 0 1 0 525.44512 371.4048 371.4048 0 0 1-262.656 108.8256 371.39968 371.39968 0 0 1-262.65088-108.8256 371.59936 371.59936 0 0 1 0-525.44512 371.39456 371.39456 0 0 1 262.656-108.82048z m0 79.62624a291.79392 291.79392 0 0 0-206.35648 85.504 291.95264 291.95264 0 0 0 0 412.8256 291.80416 291.80416 0 0 0 412.71808 0 291.95264 291.95264 0 0 0 0-412.8256 291.7888 291.7888 0 0 0-206.3616-85.504z"
        fill="#707070"
        p-id="15400"
      ></path>
    </svg>
  )
}

export interface SearchProps {
  /**
   * @default 'Search'
   */
  placeholder?: string
  /**
   * event focus
   */
  onFocus?: () => void
  /**
   * event blur
   */
  onBlur?: () => void
  /**
   * event change
   */
  onChange?: (value?: string) => void
  /**
   * event change
   */
  onSearch?: (value?: string) => void
}

export function Search({
  placeholder = 'Search by API name or description',
  onFocus,
  onBlur,
  onChange,
  onSearch,
}: SearchProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const router = useRouter()

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value
    setSearchQuery(value)
    onChange?.(value)
  }

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      handleSearch(searchQuery)
    }
  }

  const handleSearch = (keyword?: string) => {
    const value = keyword?.trim()
    if (!value) return
    router.push(`/search?keyword=${encodeURIComponent(value)}`)
    setSearchQuery('')
    if (onSearch) {
      onSearch(value)
    }
  }

  return (
    <>
      <div className="hover:bg-inputBg focus-within:bg-inputBg flex h-[42px] items-center overflow-hidden rounded-[4px] bg-[#f6f6f8] pl-3 pr-[7px] transition-all focus-within:border focus-within:border-[#707070]">
        <input
          type="text"
          placeholder={placeholder}
          className="flex-1 bg-transparent font-['Open_Sans'] text-[13px] leading-[20px] text-[#707070] outline-none placeholder:text-[#707070]"
          value={searchQuery}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onFocus={onFocus}
          onBlur={onBlur}
        />
        <span
          className="ml-3 flex size-[28px] flex-shrink-0 cursor-pointer items-center justify-center rounded text-[#707070] transition-all hover:bg-white hover:text-[#19191A]"
          onClick={() => handleSearch(searchQuery)}
        >
          <i className="iconfont iconsearch-daohangsousuo text-[16px]"></i>
        </span>
      </div>
    </>
  )
}

export default Search
