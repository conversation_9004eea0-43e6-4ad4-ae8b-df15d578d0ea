import { useState } from 'react'
import Icon, { CheckOutlined } from '@ant-design/icons'

const IconCopy = () => {
  return (
    <svg
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="15624"
      width="16"
      height="16"
      fill="currentColor"
    >
      <path
        d="M394.688 106.688h448c41.216 0 74.624 33.408 74.624 74.624v448c0 41.28-33.408 74.688-74.624 74.688h-448A74.688 74.688 0 0 1 320 629.312v-448c0-41.216 33.408-74.624 74.688-74.624z m0 64A10.688 10.688 0 0 0 384 181.312v448c0 5.888 4.8 10.688 10.688 10.688h448a10.688 10.688 0 0 0 10.624-10.688v-448a10.688 10.688 0 0 0-10.624-10.624h-448zM640 768a32 32 0 0 1 64 0v74.688c0 41.216-33.408 74.624-74.688 74.624h-448a74.688 74.688 0 0 1-74.624-74.624v-448C106.688 353.408 140.096 320 181.312 320H256a32 32 0 1 1 0 64H181.312a10.688 10.688 0 0 0-10.624 10.688v448c0 5.888 4.736 10.624 10.624 10.624h448a10.688 10.688 0 0 0 10.688-10.624V768z"
        p-id="15625"
      ></path>
    </svg>
  )
}

const CopyIcon = (props: any) => <Icon component={IconCopy} {...props} />
export const Code = ({
  codeString,
  children,
}: React.PropsWithChildren<{ codeString: string }>) => {
  const [isCopied, setIsCopied] = useState(false)
  const handleCopy = () => {
    navigator.clipboard.writeText(codeString).then(() => {
      setIsCopied(true)
      setTimeout(() => setIsCopied(false), 3000)
    })
  }

  return (
    <div className="relative overflow-hidden rounded-lg">
      <span
        className="absolute right-4 top-3 flex size-[30px] cursor-pointer items-center justify-center rounded-full bg-white text-textColorGray transition-all hover:shadow-custom"
        onClick={handleCopy}
      >
        {isCopied ? (
          <CheckOutlined style={{ color: '#10A300' }} />
        ) : (
          <CopyIcon style={{ color: '#707070' }} />
        )}
      </span>
      {children}
    </div>
  )
}
export default Code
